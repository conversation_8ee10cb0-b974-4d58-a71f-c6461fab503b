{"name": "ursus-contracts", "version": "1.0.0", "description": "Smart contracts for URSUS AI Agent Platform on Core DAO", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:testnet": "hardhat run scripts/deploy.js --network coreTestnet", "deploy:mainnet": "hardhat run scripts/deploy.js --network coreMainnet", "verify:testnet": "hardhat verify --network coreTestnet", "verify:mainnet": "hardhat verify --network coreMainnet", "node": "hardhat node", "clean": "hardhat clean"}, "keywords": ["blockchain", "ethereum", "core-dao", "ai", "defi", "smart-contracts"], "author": "URSUS Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.0.0", "dotenv": "^16.3.1", "hardhat": "^2.19.0"}}