{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Documents/GitHub/ursus/contracts/AgentFactory.sol": {"lastModificationDate": 1753574946024, "contentHash": "b536d7ce54c7522833e57938c1313748", "sourceName": "contracts/AgentFactory.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./AgentToken.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["AgentFactory"]}, "/Users/<USER>/Documents/GitHub/ursus/contracts/AgentToken.sol": {"lastModificationDate": 1753574965410, "contentHash": "962aec4d3d6b80304646455679483cc5", "sourceName": "contracts/AgentToken.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol"], "versionPragmas": ["^0.8.24"], "artifacts": ["AgentToken"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"lastModificationDate": 1753574901454, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1753574901470, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/utils/Pausable.sol": {"lastModificationDate": 1753574901455, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1753574925613, "contentHash": "59dfce11284f2636db261df9b6a18f81", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1753574901460, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1753574901488, "contentHash": "5041977bbe908de2e6ed0270447f79ad", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.8.4"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1753574925613, "contentHash": "9261adf6457863de3e9892f51317ec89", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": [">=0.4.16"], "artifacts": ["IERC20"]}, "/Users/<USER>/Documents/GitHub/ursus/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1753574936730, "contentHash": "513778b30d2750f5d2b9b19bbcf748a5", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.24", "settings": {"evmVersion": "shanghai", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": [">=0.6.2"], "artifacts": ["IERC20Metadata"]}}}