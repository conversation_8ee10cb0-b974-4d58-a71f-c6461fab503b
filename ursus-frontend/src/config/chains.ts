import { define<PERSON>hain } from 'viem'

// Core DAO Testnet Configuration
export const coreTestnet = defineChain({
  id: 1114,
  name: 'Core Testnet',
  network: 'core-testnet',
  nativeCurrency: {
    decimals: 18,
    name: 'tCORE2',
    symbol: 'tCORE2',
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.test2.btcs.network'],
    },
    public: {
      http: ['https://rpc.test2.btcs.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Core Scan',
      url: 'https://scan.test2.btcs.network',
    },
  },
  testnet: true,
})

// Core DAO Mainnet Configuration
export const coreMainnet = defineChain({
  id: 1116,
  name: 'Core',
  network: 'core',
  nativeCurrency: {
    decimals: 18,
    name: 'CORE',
    symbol: 'CORE',
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.coredao.org'],
    },
    public: {
      http: ['https://rpc.coredao.org'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON> Scan',
      url: 'https://scan.coredao.org',
    },
  },
})

// Contract Addresses for Core Testnet
export const CORE_TESTNET_CONTRACTS = {
  // Core ecosystem precompiled contracts
  VALIDATOR_SET: '******************************************',
  SLASH_INDICATOR: '******************************************',
  SYSTEM_REWARD: '******************************************',
  BTC_LIGHT_CLIENT: '******************************************',
  RELAYER_HUB: '******************************************',
  CANDIDATE_HUB: '******************************************',
  GOV_HUB: '******************************************',
  PLEDGE_AGENT: '******************************************',
  BURN: '******************************************',
  FOUNDATION: '******************************************',
  STAKE_HUB: '******************************************',
  CORE_AGENT: '******************************************',
  HASH_AGENT: '******************************************',
  BTC_AGENT: '******************************************',
  BTC_STAKE: '******************************************',
  BTCLST_STAKE: '******************************************',
  BTCLST_TOKEN: '******************************************',
  
  // URSUS Platform Contracts (to be deployed)
  AGENT_FACTORY: '', // Will be set after deployment
  TOKEN_FACTORY: '', // Will be set after deployment
  BONDING_CURVE: '', // Will be set after deployment
  PLATFORM_TREASURY: '', // Will be set after deployment
} as const

// Contract Addresses for Core Mainnet
export const CORE_MAINNET_CONTRACTS = {
  // Core ecosystem precompiled contracts (same addresses)
  ...CORE_TESTNET_CONTRACTS,
  
  // URSUS Platform Contracts (to be deployed)
  AGENT_FACTORY: '', // Will be set after deployment
  TOKEN_FACTORY: '', // Will be set after deployment
  BONDING_CURVE: '', // Will be set after deployment
  PLATFORM_TREASURY: '', // Will be set after deployment
} as const

// Platform Configuration
export const PLATFORM_CONFIG = {
  PLATFORM_FEE_PERCENTAGE: 250, // 2.5% in basis points
  CREATOR_ROYALTY_PERCENTAGE: 500, // 5% in basis points
  LIQUIDITY_POOL_PERCENTAGE: 9250, // 92.5% in basis points
  MIN_TOKEN_SUPPLY: 1_000_000,
  MAX_TOKEN_SUPPLY: 1_000_000_000,
  INITIAL_TOKEN_PRICE: '0.001', // in CORE
  BONDING_CURVE_RESERVE_RATIO: 500000, // 50% in PPM (parts per million)
} as const

// Supported Networks
export const SUPPORTED_CHAINS = [coreTestnet, coreMainnet] as const

// Default chain for development
export const DEFAULT_CHAIN = coreTestnet
