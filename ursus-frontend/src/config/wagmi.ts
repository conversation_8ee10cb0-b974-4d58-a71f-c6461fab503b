import { configureChains, createConfig } from 'wagmi'
import { publicProvider } from 'wagmi/providers/public'
import { MetaMaskConnector } from 'wagmi/connectors/metaMask'
import { WalletConnectConnector } from 'wagmi/connectors/walletConnect'
import { InjectedConnector } from 'wagmi/connectors/injected'
import { coreTestnet, coreMainnet, SUPPORTED_CHAINS } from './chains'

// Configure chains and providers
const { chains, publicClient, webSocketPublicClient } = configureChains(
  SUPPORTED_CHAINS,
  [publicProvider()]
)

// Configure connectors
const connectors = [
  new MetaMaskConnector({
    chains,
    options: {
      shimDisconnect: true,
    },
  }),
  new WalletConnectConnector({
    chains,
    options: {
      projectId: process.env.VITE_WALLETCONNECT_PROJECT_ID || 'your-project-id',
      metadata: {
        name: 'URSUS AI Agent Platform',
        description: 'AI Agent + Token Launchpad on Core DAO',
        url: 'https://ursus.ai',
        icons: ['https://ursus.ai/icon.png'],
      },
    },
  }),
  new InjectedConnector({
    chains,
    options: {
      name: 'Injected',
      shimDisconnect: true,
    },
  }),
]

// Create wagmi config
export const wagmiConfig = createConfig({
  autoConnect: true,
  connectors,
  publicClient,
  webSocketPublicClient,
})

export { chains }

// Export specific chains for easy access
export { coreTestnet, coreMainnet }
