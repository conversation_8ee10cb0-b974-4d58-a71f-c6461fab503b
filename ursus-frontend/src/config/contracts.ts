// Contract addresses for different networks
export const CONTRACT_ADDRESSES = {
  CORE_TESTNET: {
    AGENT_FACTORY: '0x0000000000000000000000000000000000000000', // Will be updated after deployment
    CHAIN_ID: 1114,
  },
  CORE_MAINNET: {
    AGENT_FACTORY: '0x0000000000000000000000000000000000000000', // Will be updated after deployment
    CHAIN_ID: 1116,
  }
} as const;

// Contract ABIs
export const AGENT_FACTORY_ABI = [
  {
    "inputs": [
      {"name": "_name", "type": "string"},
      {"name": "_symbol", "type": "string"},
      {"name": "_description", "type": "string"},
      {"name": "_instructions", "type": "string"},
      {"name": "_model", "type": "string"},
      {"name": "_category", "type": "string"}
    ],
    "name": "createAgent",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "creationFee",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getTotalAgents",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"name": "_offset", "type": "uint256"},
      {"name": "_limit", "type": "uint256"}
    ],
    "name": "getAllAgents",
    "outputs": [
      {"name": "agents", "type": "address[]"},
      {"name": "total", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"name": "_limit", "type": "uint256"}],
    "name": "getTrendingAgents",
    "outputs": [{"name": "", "type": "address[]"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"name": "_creator", "type": "address"}],
    "name": "getAgentsByCreator",
    "outputs": [{"name": "", "type": "address[]"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"name": "", "type": "address"}],
    "name": "agentMetadata",
    "outputs": [
      {"name": "tokenAddress", "type": "address"},
      {"name": "name", "type": "string"},
      {"name": "symbol", "type": "string"},
      {"name": "description", "type": "string"},
      {"name": "category", "type": "string"},
      {"name": "creator", "type": "address"},
      {"name": "createdAt", "type": "uint256"},
      {"name": "isActive", "type": "bool"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "name": "tokenAddress", "type": "address"},
      {"indexed": true, "name": "creator", "type": "address"},
      {"indexed": false, "name": "name", "type": "string"},
      {"indexed": false, "name": "symbol", "type": "string"},
      {"indexed": false, "name": "description", "type": "string"},
      {"indexed": false, "name": "category", "type": "string"}
    ],
    "name": "AgentCreated",
    "type": "event"
  }
] as const;

export const AGENT_TOKEN_ABI = [
  {
    "inputs": [],
    "name": "purchaseTokens",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [{"name": "_tokenAmount", "type": "uint256"}],
    "name": "sellTokens",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"name": "_coreAmount", "type": "uint256"}],
    "name": "calculatePurchaseReturn",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"name": "_tokenAmount", "type": "uint256"}],
    "name": "calculateSaleReturn",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getCurrentPrice",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getBondingCurveInfo",
    "outputs": [
      {"name": "supply", "type": "uint256"},
      {"name": "reserve", "type": "uint256"},
      {"name": "price", "type": "uint256"},
      {"name": "marketCap", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getAgentInfo",
    "outputs": [
      {"name": "description", "type": "string"},
      {"name": "instructions", "type": "string"},
      {"name": "model", "type": "string"},
      {"name": "agentCreator", "type": "address"},
      {"name": "timestamp", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"name": "account", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "totalSupply",
    "outputs": [{"name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "name",
    "outputs": [{"name": "", "type": "string"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "symbol",
    "outputs": [{"name": "", "type": "string"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "name": "buyer", "type": "address"},
      {"indexed": false, "name": "coreAmount", "type": "uint256"},
      {"indexed": false, "name": "tokensReceived", "type": "uint256"}
    ],
    "name": "TokensPurchased",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "name": "seller", "type": "address"},
      {"indexed": false, "name": "tokensAmount", "type": "uint256"},
      {"indexed": false, "name": "coreReceived", "type": "uint256"}
    ],
    "name": "TokensSold",
    "type": "event"
  }
] as const;

// Helper function to get contract address for current network
export const getContractAddress = (contractName: 'AGENT_FACTORY', chainId: number): string => {
  if (chainId === 1114) {
    return CONTRACT_ADDRESSES.CORE_TESTNET[contractName];
  } else if (chainId === 1116) {
    return CONTRACT_ADDRESSES.CORE_MAINNET[contractName];
  }
  throw new Error(`Unsupported chain ID: ${chainId}`);
};
