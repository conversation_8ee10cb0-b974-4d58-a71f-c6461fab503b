import React, { useState, useCallback } from 'react';
import Sidebar from './components/Sidebar';
import NotificationBar from './components/NotificationBar';
import SearchBar from './components/SearchBar';
import TrendingSection from './components/TrendingSection';
import FilterBar from './components/FilterBar';
import AgentGrid from './components/AgentGrid';
import AgentCreation from './components/AgentCreation';
import Profile from './components/Profile';
import More from './components/More';
import { mockAgents, trendingAgents } from './data/mockData';
import { useRealtimeUpdates } from './hooks/useRealtimeUpdates';
import { Agent } from './types';

function App() {
  const [activeSection, setActiveSection] = useState('home');
  const [searchQuery, setSearchQuery] = useState('');
  const [includeNsfw, setIncludeNsfw] = useState(false);
  const [sortBy, setSortBy] = useState('featured');
  
  const realtimeAgents = useRealtimeUpdates(mockAgents);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleCardClick = useCallback((agent: Agent) => {
    console.log('Opening agent details:', agent.name);
  }, []);

  const handleChatClick = useCallback((agent: Agent) => {
    console.log('Starting chat with:', agent.name);
  }, []);

  const handleTradeClick = useCallback((agent: Agent) => {
    console.log('Opening trade modal for:', agent.name);
  }, []);

  const filteredAgents = realtimeAgents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.symbol.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesNsfw = includeNsfw || !agent.isNsfw;
    
    return matchesSearch && matchesNsfw;
  });

  const sortedAgents = [...filteredAgents].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'market-cap':
        return b.marketCap - a.marketCap;
      case 'chats':
        return b.chatCount - a.chatCount;
      default: // featured
        return b.marketCap - a.marketCap;
    }
  });

  return (
    <div className="min-h-screen bg-[#0a0a0a]">
      <Sidebar 
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      
      {activeSection === 'agent-creation' ? (
        <AgentCreation onBack={() => setActiveSection('home')} />
      ) : activeSection === 'profile' ? (
        <Profile onBack={() => setActiveSection('home')} />
      ) : activeSection === 'more' ? (
        <More onBack={() => setActiveSection('home')} />
      ) : (
        <div className="ml-[200px]">
          <NotificationBar />
          
          <main className="p-5">
            <SearchBar onSearch={handleSearch} />
            
            <TrendingSection trendingAgents={trendingAgents} />
            
            <FilterBar
              includeNsfw={includeNsfw}
              sortBy={sortBy}
              onToggleNsfw={() => setIncludeNsfw(!includeNsfw)}
              onSortChange={setSortBy}
            />
            
            <AgentGrid
              agents={sortedAgents}
              onCardClick={handleCardClick}
              onChatClick={handleChatClick}
              onTradeClick={handleTradeClick}
            />
          </main>
        </div>
      )}
    </div>
  );
}

export default App;