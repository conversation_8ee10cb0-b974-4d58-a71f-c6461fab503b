import React from 'react';
import { Agent } from '../types';

interface TrendingSectionProps {
  trendingAgents: Agent[];
}

const TrendingSection: React.FC<TrendingSectionProps> = ({ trendingAgents }) => {
  return (
    <div className="mb-8">
      <h2 className="text-white text-lg font-semibold mb-4">Now trending</h2>
      
      <div className="flex gap-4 overflow-x-auto scrollbar-hide pb-2">
        {trendingAgents.map((agent) => (
          <div
            key={agent.id}
            className="flex-shrink-0 w-[300px] h-[180px] bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4 hover:border-[#d8e9ea] transition-colors cursor-pointer"
          >
            <div className="flex items-start gap-3 mb-3">
              <div className="w-12 h-12 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-full flex items-center justify-center text-black font-bold">
                {agent.name.charAt(0)}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-white font-medium">{agent.name}</span>
                  <span className="text-[#a0a0a0] text-sm">({agent.symbol})</span>
                </div>
                <div className="text-[#10b981] text-sm font-medium">
                  ${(agent.marketCap / 1000000).toFixed(1)}M
                </div>
              </div>
            </div>
            
            <div className="text-[#e5e5e5] text-sm mb-3 line-clamp-3">
              {agent.description}
            </div>
            
            <div className="flex items-center justify-between text-[#a0a0a0] text-sm">
              <span>{agent.chatCount} chats</span>
              <span>{agent.createdAt}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TrendingSection;