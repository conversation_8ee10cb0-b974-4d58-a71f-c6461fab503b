import React from 'react';
import { Home, Monitor, User, MoreHorizontal, Plus, TrendingUp, Wallet, Eye, ChevronRight } from 'lucide-react';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const navigationItems = [
    { id: 'home', label: 'Home', icon: Home },
    { id: 'agent-creation', label: 'Agent Creation', icon: Monitor },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'more', label: 'More', icon: MoreHorizontal },
  ];

  const holdings = [
    { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'DEFI', value: '$0.12' },
    { name: 'Content AI', symbol: 'WRITE', value: '$0.08' },
    { name: '<PERSON><PERSON> Hunter', symbol: 'YIELD', value: '$0.23' },
  ];

  return (
    <div className="fixed left-0 top-0 w-[200px] h-screen bg-[#1a1a1a] border-r border-[#2a2a2a] flex flex-col">
      {/* Navigation */}
      <div className="p-4 space-y-1">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeSection === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors ${
                isActive 
                  ? 'bg-[#d8e9ea] text-black' 
                  : 'text-[#e5e5e5] hover:bg-[#2a2a2a] hover:text-white'
              }`}
            >
              <Icon size={18} />
              {item.label}
            </button>
          );
        })}
      </div>

      {/* Create Agent Button */}
      <div className="px-4 mb-6">
        <button 
          onClick={() => onSectionChange('agent-creation')}
          className="w-full bg-[#d8e9ea] text-black font-semibold py-2.5 px-4 rounded-lg hover:bg-[#b8d4d6] transition-colors flex items-center justify-center gap-2"
        >
          <Plus size={16} />
          Create Agent
        </button>
      </div>

      {/* Holdings Section */}
      <div className="px-4 flex-1">
        <div className="border-t border-[#2a2a2a] pt-6">
          {/* Section Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                <Wallet size={14} className="text-black" />
              </div>
              <span className="text-white text-sm font-semibold">Creator Rewards</span>
              <div className="bg-gradient-to-r from-[#10b981] to-[#059669] text-white text-xs px-2 py-0.5 rounded-full font-medium shadow-sm">
                new
              </div>
            </div>
            <button className="text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors">
              <Eye size={14} />
            </button>
          </div>
          
          {/* Total Balance Card */}
          <div className="bg-gradient-to-br from-[#1e1e1e] to-[#2a2a2a] rounded-xl p-4 mb-4 border border-[#3a3a3a] shadow-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-[#a0a0a0] text-xs font-medium uppercase tracking-wide">Total Balance</span>
              <div className="flex items-center gap-1 text-[#10b981] text-xs">
                <TrendingUp size={12} />
                <span>+12.3%</span>
              </div>
            </div>
            <div className="text-white text-2xl font-bold mb-1">$0.43</div>
            <div className="text-[#a0a0a0] text-xs">≈ 0.0014 SOL</div>
          </div>
          
          {/* Holdings List */}
          <div className="space-y-1">
            {holdings.map((holding, index) => (
              <div 
                key={index} 
                className="group bg-[#1e1e1e] hover:bg-[#252525] rounded-lg p-3 transition-all duration-200 cursor-pointer border border-transparent hover:border-[#3a3a3a]"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1">
                    {/* Token Icon */}
                    <div className="w-8 h-8 bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] rounded-lg flex items-center justify-center shadow-sm">
                      <span className="text-black text-xs font-bold">
                        {holding.symbol.charAt(0)}
                      </span>
                    </div>
                    
                    {/* Token Info */}
                    <div className="flex-1 min-w-0">
                      <div className="text-[#e5e5e5] text-sm font-medium truncate">
                        {holding.name}
                      </div>
                      <div className="text-[#a0a0a0] text-xs font-medium">
                        {holding.symbol}
                      </div>
                    </div>
                  </div>
                  
                  {/* Value and Arrow */}
                  <div className="flex items-center gap-2">
                    <div className="text-right">
                      <div className="text-[#10b981] text-sm font-semibold">
                        {holding.value}
                      </div>
                      <div className="text-[#666] text-xs">
                        +{(Math.random() * 5 + 1).toFixed(1)}%
                      </div>
                    </div>
                    <ChevronRight 
                      size={14} 
                      className="text-[#666] group-hover:text-[#a0a0a0] transition-colors"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* See More Button */}
          <button className="w-full mt-4 text-[#d8e9ea] text-sm font-medium hover:text-white transition-colors py-2 px-3 rounded-lg hover:bg-[#1e1e1e] flex items-center justify-center gap-2 group">
            <span>View All Holdings</span>
            <ChevronRight size={14} className="group-hover:translate-x-1 transition-transform" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;