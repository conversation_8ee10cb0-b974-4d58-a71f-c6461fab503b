import React from 'react';
import { MessageCircle, TrendingUp } from 'lucide-react';
import { Agent } from '../types';
import Mini<PERSON><PERSON> from './MiniChart';

interface AgentCardProps {
  agent: Agent;
  onCardClick: (agent: Agent) => void;
  onChatClick: (agent: Agent) => void;
  onTradeClick: (agent: Agent) => void;
}

const AgentCard: React.FC<AgentCardProps> = ({ 
  agent, 
  onCardClick, 
  onChatClick, 
  onTradeClick 
}) => {
  const formatMarketCap = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(0)}`;
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const created = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div 
      className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-5 hover:border-[#d8e9ea] transition-all duration-300 hover:shadow-xl hover:shadow-[#d8e9ea]/10 cursor-pointer group relative overflow-hidden"
      onClick={() => onCardClick(agent)}
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-[#d8e9ea]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
      
      {/* Header with Avatar and Chart */}
      <div className="flex items-start justify-between mb-4 relative z-10">
        <div className="flex items-start gap-4 flex-1 min-w-0">
          {/* Professional Avatar */}
          <div className="relative flex-shrink-0">
            <div className="w-14 h-14 relative group-hover:scale-105 transition-transform duration-300">
              {/* Outer glow ring */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-[#d8e9ea] via-[#b8d4d6] to-[#d8e9ea] rounded-xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity"></div>
              
              {/* Main avatar container */}
              <div className="relative w-full h-full bg-gradient-to-br from-[#f8fffe] to-[#e8f4f5] rounded-xl shadow-lg flex items-center justify-center border border-[#d8e9ea]/20">
                <span className="text-xl filter drop-shadow-sm">
                  {agent.avatar || '🤖'}
                </span>
              </div>
              
              {/* Status indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-[#10b981] rounded-full border-2 border-[#1a1a1a] shadow-sm">
                <div className="w-full h-full bg-[#10b981] rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
          
          {/* Agent Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-white font-semibold text-base truncate">
                {agent.name}
              </h3>
              <span className="text-[#a0a0a0] text-sm font-medium">
                ({agent.symbol})
              </span>
            </div>
            
            <div className="text-[#a0a0a0] text-xs mb-2">
              created by 🤖 <span className="text-[#d8e9ea]">{agent.creator}</span> • {getTimeAgo(agent.createdAt)}
            </div>
            
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <span className="text-[#a0a0a0]">mcap:</span>
                <span className="text-[#10b981] font-semibold">{formatMarketCap(agent.marketCap)}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-[#a0a0a0]">chats:</span>
                <span className="text-[#d8e9ea] font-semibold">{agent.chatCount}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Chart */}
        <div className="flex-shrink-0 ml-2">
          <MiniChart 
            data={agent.priceHistory} 
            priceChange={agent.priceChange24h}
            width={75}
            height={35}
          />
        </div>
      </div>

      {/* Description */}
      <div className="mb-4 relative z-10">
        <p className="text-[#e5e5e5] text-sm leading-relaxed line-clamp-2">
          {agent.description}
        </p>
      </div>

      {/* Action Buttons */}
      <div className="absolute bottom-5 left-5 right-5 flex items-center justify-between opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
        <div className="text-[#666] text-xs">
          {agent.category}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onChatClick(agent);
            }}
            className="bg-[#2a2a2a]/90 backdrop-blur-sm text-[#d8e9ea] px-3 py-1.5 rounded-lg text-xs font-medium hover:bg-[#3a3a3a] transition-all duration-200 flex items-center gap-1.5 border border-[#d8e9ea]/20"
          >
            <MessageCircle size={12} />
            Chat
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onTradeClick(agent);
            }}
            className="bg-[#d8e9ea] text-black px-3 py-1.5 rounded-lg text-xs font-semibold hover:bg-[#b8d4d6] transition-all duration-200 flex items-center gap-1.5 shadow-sm"
          >
            <TrendingUp size={12} />
            Trade
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentCard;