import React from 'react';
import { ArrowLeft, ExternalLink, Mail, FileText, Shield, Calendar, Github } from 'lucide-react';

interface MoreProps {
  onBack: () => void;
}

interface FeatureCard {
  id: string;
  icon: string;
  title: string;
  description: string;
  status?: 'coming-soon' | 'available' | 'beta';
  externalLink?: string;
  onClick?: () => void;
}

const More: React.FC<MoreProps> = ({ onBack }) => {
  const features: FeatureCard[] = [
    {
      id: 'analytics',
      icon: '📊',
      title: 'Advanced Analytics',
      description: 'Deep insights into agent performance and market trends',
      status: 'coming-soon'
    },
    {
      id: 'api',
      icon: '🔗',
      title: 'Developer API',
      description: 'Integrate Ursus agents into your applications',
      status: 'available',
      externalLink: 'https://docs.ursus.ai'
    },
    {
      id: 'marketplace',
      icon: '🏪',
      title: 'Agent Templates',
      description: 'Pre-built agent configurations for quick deployment',
      status: 'available'
    },
    {
      id: 'staking',
      icon: '💎',
      title: '<PERSON><PERSON> & <PERSON>arn',
      description: 'Stake agent tokens for passive income and benefits',
      status: 'beta'
    },
    {
      id: 'community',
      icon: '💬',
      title: 'Community',
      description: 'Connect with other creators and share strategies',
      status: 'available',
      externalLink: 'https://community.ursus.ai'
    },
    {
      id: 'support',
      icon: '❓',
      title: 'Support',
      description: 'Documentation, tutorials, and customer support',
      status: 'available',
      externalLink: 'https://help.ursus.ai'
    },
    {
      id: 'stats',
      icon: '📈',
      title: 'Platform Metrics',
      description: 'Real-time platform statistics and growth metrics',
      status: 'available'
    },
    {
      id: 'bugs',
      icon: '🐛',
      title: 'Report Issues',
      description: 'Help us improve by reporting bugs and feedback',
      status: 'available',
      externalLink: 'https://github.com/ursus/ursus/issues'
    },
    {
      id: 'roadmap',
      icon: '🗺️',
      title: 'Roadmap',
      description: 'See what features are coming next to Ursus',
      status: 'available',
      externalLink: 'https://roadmap.ursus.ai'
    }
  ];

  const platformInfo = {
    version: 'v1.2.0',
    lastUpdate: 'December 15, 2024',
    contact: '<EMAIL>',
    github: 'https://github.com/ursus/ursus'
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'coming-soon': return 'bg-[#f59e0b] text-black';
      case 'beta': return 'bg-[#d8e9ea] text-black';
      case 'available': return 'bg-[#10b981] text-white';
      default: return 'bg-[#2a2a2a] text-[#a0a0a0]';
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'coming-soon': return 'Coming Soon';
      case 'beta': return 'Beta';
      case 'available': return 'Available';
      default: return 'Unknown';
    }
  };

  const handleCardClick = (feature: FeatureCard) => {
    if (feature.externalLink) {
      window.open(feature.externalLink, '_blank');
    } else if (feature.onClick) {
      feature.onClick();
    }
  };

  return (
    <div className="min-h-screen bg-[#0a0a0a] ml-[200px]">
      <div className="p-8">
        {/* Header Section */}
        <div className="mb-8">
          {/* Back Button */}
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-[#a0a0a0] hover:text-white mb-6 transition-colors group"
          >
            <ArrowLeft size={16} className="group-hover:-translate-x-1 transition-transform" />
            <span className="text-sm">Back to Home</span>
          </button>

          {/* Title */}
          <h1 className="text-white text-4xl font-bold mb-3 bg-gradient-to-r from-white to-[#d8e9ea] bg-clip-text text-transparent">
            More Features
          </h1>
          <p className="text-[#e5e5e5] text-lg">
            Explore additional tools, resources, and platform information
          </p>
        </div>

        {/* Feature Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {features.map((feature) => (
            <div
              key={feature.id}
              onClick={() => handleCardClick(feature)}
              className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-8 hover:border-[#d8e9ea] hover:scale-[1.02] transition-all duration-300 cursor-pointer group relative"
              style={{ minHeight: '200px' }}
            >
              {/* Status Badge */}
              {feature.status && (
                <div className={`absolute top-4 right-4 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(feature.status)}`}>
                  {getStatusText(feature.status)}
                </div>
              )}

              {/* Icon */}
              <div className="text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>

              {/* Content */}
              <div className="space-y-3">
                <h3 className="text-white text-xl font-semibold group-hover:text-[#d8e9ea] transition-colors">
                  {feature.title}
                </h3>
                <p className="text-[#a0a0a0] text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>

              {/* External Link Indicator */}
              {feature.externalLink && (
                <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <ExternalLink size={16} className="text-[#d8e9ea]" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Platform Information Section */}
        <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Version Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-[#a0a0a0] text-sm">
                <Shield size={16} />
                <span>Version</span>
              </div>
              <div className="text-white font-medium">{platformInfo.version}</div>
            </div>

            {/* Last Update */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-[#a0a0a0] text-sm">
                <Calendar size={16} />
                <span>Last Update</span>
              </div>
              <div className="text-white font-medium">{platformInfo.lastUpdate}</div>
            </div>

            {/* Contact */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-[#a0a0a0] text-sm">
                <Mail size={16} />
                <span>Contact</span>
              </div>
              <a 
                href={`mailto:${platformInfo.contact}`}
                className="text-[#d8e9ea] font-medium hover:text-[#b8d4d6] transition-colors"
              >
                {platformInfo.contact}
              </a>
            </div>

            {/* GitHub */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-[#a0a0a0] text-sm">
                <Github size={16} />
                <span>GitHub</span>
              </div>
              <a 
                href={platformInfo.github}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#d8e9ea] font-medium hover:text-[#b8d4d6] transition-colors flex items-center gap-1"
              >
                View Source
                <ExternalLink size={12} />
              </a>
            </div>
          </div>

          {/* Legal Links */}
          <div className="border-t border-[#2a2a2a] mt-8 pt-8">
            <div className="flex flex-wrap gap-6 text-sm">
              <a 
                href="/terms"
                className="text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors flex items-center gap-2"
              >
                <FileText size={14} />
                Terms of Service
              </a>
              <a 
                href="/privacy"
                className="text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors flex items-center gap-2"
              >
                <Shield size={14} />
                Privacy Policy
              </a>
              <a 
                href="/cookies"
                className="text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors flex items-center gap-2"
              >
                <FileText size={14} />
                Cookie Policy
              </a>
              <a 
                href="/security"
                className="text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors flex items-center gap-2"
              >
                <Shield size={14} />
                Security
              </a>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-[#2a2a2a] mt-6 pt-6">
            <p className="text-[#666] text-sm text-center">
              © 2024 Ursus. All rights reserved. Built with ❤️ for the decentralized AI community.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default More; 