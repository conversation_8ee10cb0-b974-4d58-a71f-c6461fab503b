import React, { useState } from 'react';
import { 
  ArrowLeft, 
  Edit, 
  BarChart3, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Star, 
  Twitter,
  Github,
  Calendar,
  Shield,
  Settings,
  Activity,
  Wallet,
  Copy,
  Eye,
  EyeOff,
  Key
} from 'lucide-react';

interface ProfileProps {
  onBack: () => void;
}

interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'high-demand';
  chatCount: number;
  earnings: number;
  rating: number;
  icon: string;
}

interface PortfolioItem {
  token: string;
  symbol: string;
  holdings: number;
  value: number;
  change24h: number;
  icon: string;
}

interface ActivityItem {
  id: string;
  type: 'created' | 'earned' | 'milestone' | 'trade';
  message: string;
  amount?: number;
  timestamp: string;
  icon: string;
}

const Profile: React.FC<ProfileProps> = ({ onBack }) => {
  const [activeTab, setActiveTab] = useState('agents');
  const [isEditingBio, setIsEditingBio] = useState(false);
  const [bio, setBio] = useState('AI enthusiast and De<PERSON>i researcher. Building the future of decentralized AI agents.');
  const [showPassword, setShowPassword] = useState(false);

  // Mock data
  const userStats = {
    agentsCreated: 12,
    totalVolume: 45600,
    successRate: 94.5,
    followers: 1234
  };

  const userProfile = {
    username: 'alex_crypto',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
    joinDate: 'March 2023',
    isVerified: true,
    socialLinks: {
      twitter: 'https://twitter.com/alex_crypto',
      discord: 'alex_crypto#1234',
      github: 'https://github.com/alex_crypto'
    }
  };

  const agents: Agent[] = [
    {
      id: '1',
      name: 'DeFi Analyzer Pro',
      description: 'Advanced DeFi research assistant with real-time market analysis',
      status: 'active',
      chatCount: 1247,
      earnings: 2340.50,
      rating: 4.8,
      icon: '💎'
    },
    {
      id: '2',
      name: 'Content Creator AI',
      description: 'Creative content generator for social media and marketing',
      status: 'high-demand',
      chatCount: 892,
      earnings: 1890.25,
      rating: 4.9,
      icon: '📝'
    },
    {
      id: '3',
      name: 'Trading Signal Bot',
      description: 'Cryptocurrency trading signals and market analysis',
      status: 'paused',
      chatCount: 567,
      earnings: 1234.75,
      rating: 4.6,
      icon: '⚡'
    }
  ];

  const portfolioItems: PortfolioItem[] = [
    {
      token: 'DeFi Analyzer Token',
      symbol: 'DAT',
      holdings: 50000,
      value: 1250.00,
      change24h: 12.5,
      icon: '💎'
    },
    {
      token: 'Content Creator Token',
      symbol: 'CCT',
      holdings: 25000,
      value: 875.50,
      change24h: -3.2,
      icon: '📝'
    },
    {
      token: 'Trading Signal Token',
      symbol: 'TST',
      holdings: 15000,
      value: 450.25,
      change24h: 8.7,
      icon: '⚡'
    }
  ];

  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'created',
      message: 'Created DeFi Analyzer Pro agent',
      timestamp: '2 hours ago',
      icon: '🤖'
    },
    {
      id: '2',
      type: 'earned',
      message: 'Earned $234.50 from agent interactions',
      amount: 234.50,
      timestamp: '1 day ago',
      icon: '💰'
    },
    {
      id: '3',
      type: 'milestone',
      message: 'Content Creator AI reached 1000 chats milestone',
      timestamp: '3 days ago',
      icon: '🎯'
    },
    {
      id: '4',
      type: 'trade',
      message: 'Bought 5000 DAT tokens',
      amount: 125.00,
      timestamp: '1 week ago',
      icon: '📈'
    }
  ];

  const tabs = [
    { id: 'agents', label: 'My Agents', icon: Users },
    { id: 'portfolio', label: 'Portfolio', icon: Wallet },
    { id: 'activity', label: 'Activity', icon: Activity },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-[#10b981]';
      case 'paused': return 'text-[#f59e0b]';
      case 'high-demand': return 'text-[#ef4444]';
      default: return 'text-[#a0a0a0]';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'active': return 'bg-[#10b981]/10';
      case 'paused': return 'bg-[#f59e0b]/10';
      case 'high-demand': return 'bg-[#ef4444]/10';
      default: return 'bg-[#2a2a2a]';
    }
  };

  return (
    <div className="min-h-screen bg-[#0a0a0a] ml-[200px]">
      {/* Header Section */}
      <div className="relative h-[200px] bg-gradient-to-b from-[#0a0a0a] to-[#1a1a1a]">
        <div className="absolute inset-0 flex items-center justify-center pt-8">
          <div className="text-center">
            <div className="w-[120px] h-[120px] rounded-full bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] mx-auto mb-4 flex items-center justify-center text-4xl font-bold text-black">
              {userProfile.username.charAt(0).toUpperCase()}
            </div>
            <div className="flex items-center justify-center gap-2 mb-2">
              <h1 className="text-white text-3xl font-bold">{userProfile.username}</h1>
              {userProfile.isVerified && (
                <div className="p-1 bg-[#d8e9ea] rounded-full">
                  <Shield size={16} className="text-black" />
                </div>
              )}
            </div>
            <div className="flex items-center justify-center gap-2 text-[#a0a0a0]">
              <Calendar size={14} />
              <span className="text-sm">Joined {userProfile.joinDate}</span>
            </div>
          </div>
        </div>
        
        {/* Back Button */}
        <button
          onClick={onBack}
          className="absolute top-6 left-6 flex items-center gap-2 text-[#a0a0a0] hover:text-white transition-colors"
        >
          <ArrowLeft size={16} />
          <span className="text-sm">Back</span>
        </button>
      </div>

      {/* Profile Info Section */}
      <div className="px-8 py-6">
        {/* Bio Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-medium">Bio</h3>
            <button
              onClick={() => setIsEditingBio(!isEditingBio)}
              className="text-[#d8e9ea] hover:text-[#b8d4d6] transition-colors"
            >
              <Edit size={16} />
            </button>
          </div>
          {isEditingBio ? (
            <textarea
              value={bio}
              onChange={(e) => setBio(e.target.value)}
              className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-4 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors resize-none"
              rows={3}
            />
          ) : (
            <p className="text-[#e5e5e5] leading-relaxed">{bio}</p>
          )}
        </div>

        {/* Social Links */}
        <div className="mb-8">
          <h3 className="text-white font-medium mb-3">Social Links</h3>
          <div className="flex gap-4">
            <a href={userProfile.socialLinks.twitter} className="flex items-center gap-2 text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors">
              <Twitter size={18} />
              <span className="text-sm">Twitter</span>
            </a>
            <a href="#" className="flex items-center gap-2 text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors">
              <Users size={18} />
              <span className="text-sm">Discord</span>
            </a>
            <a href={userProfile.socialLinks.github} className="flex items-center gap-2 text-[#a0a0a0] hover:text-[#d8e9ea] transition-colors">
              <Github size={18} />
              <span className="text-sm">GitHub</span>
            </a>
          </div>
        </div>

        {/* Quick Stats Row */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                <Users size={16} className="text-black" />
              </div>
              <span className="text-[#a0a0a0] text-sm">Agents Created</span>
            </div>
            <div className="text-2xl font-bold text-white">{userStats.agentsCreated}</div>
          </div>
          
          <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                <DollarSign size={16} className="text-black" />
              </div>
              <span className="text-[#a0a0a0] text-sm">Total Volume</span>
            </div>
            <div className="text-2xl font-bold text-white">${userStats.totalVolume.toLocaleString()}</div>
          </div>
          
          <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                <TrendingUp size={16} className="text-black" />
              </div>
              <span className="text-[#a0a0a0] text-sm">Success Rate</span>
            </div>
            <div className="text-2xl font-bold text-white">{userStats.successRate}%</div>
          </div>
          
          <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                <Users size={16} className="text-black" />
              </div>
              <span className="text-[#a0a0a0] text-sm">Followers</span>
            </div>
            <div className="text-2xl font-bold text-white">{userStats.followers.toLocaleString()}</div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-[#2a2a2a] mb-6">
          <div className="flex gap-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-3 px-1 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-[#d8e9ea] border-b-2 border-[#d8e9ea]'
                      : 'text-[#a0a0a0] hover:text-white'
                  }`}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          {/* My Agents Tab */}
          {activeTab === 'agents' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-white text-xl font-semibold">My Agents</h3>
                <button className="bg-[#d8e9ea] text-black px-4 py-2 rounded-lg font-medium hover:bg-[#b8d4d6] transition-colors">
                  Create New Agent
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {agents.map((agent) => (
                  <div key={agent.id} className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-6 hover:border-[#3a3a3a] transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] rounded-xl flex items-center justify-center text-xl">
                          {agent.icon}
                        </div>
                        <div>
                          <h4 className="text-white font-semibold">{agent.name}</h4>
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusBg(agent.status)} ${getStatusColor(agent.status)}`}>
                            {agent.status === 'active' && 'Active'}
                            {agent.status === 'paused' && 'Paused'}
                            {agent.status === 'high-demand' && 'High Demand'}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-[#a0a0a0] text-sm mb-4 line-clamp-2">{agent.description}</p>
                    
                    <div className="space-y-3 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-[#a0a0a0]">Chats</span>
                        <span className="text-white">{agent.chatCount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-[#a0a0a0]">Earnings</span>
                        <span className="text-[#10b981]">${agent.earnings.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-[#a0a0a0]">Rating</span>
                        <div className="flex items-center gap-1">
                          <Star size={14} className="text-[#f59e0b] fill-current" />
                          <span className="text-white">{agent.rating}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <button className="flex-1 bg-[#2a2a2a] text-white px-3 py-2 rounded-lg text-sm font-medium hover:bg-[#3a3a3a] transition-colors flex items-center justify-center gap-2">
                        <Edit size={14} />
                        Edit
                      </button>
                      <button className="flex-1 bg-[#d8e9ea] text-black px-3 py-2 rounded-lg text-sm font-medium hover:bg-[#b8d4d6] transition-colors flex items-center justify-center gap-2">
                        <BarChart3 size={14} />
                        Analytics
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Portfolio Tab */}
          {activeTab === 'portfolio' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-white text-xl font-semibold">Portfolio</h3>
                <div className="text-right">
                  <div className="text-[#a0a0a0] text-sm">Total Value</div>
                  <div className="text-white text-xl font-bold">${portfolioItems.reduce((sum, item) => sum + item.value, 0).toLocaleString()}</div>
                </div>
              </div>
              
              <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-[#2a2a2a]">
                        <th className="text-left p-4 text-[#a0a0a0] font-medium">Token</th>
                        <th className="text-left p-4 text-[#a0a0a0] font-medium">Holdings</th>
                        <th className="text-left p-4 text-[#a0a0a0] font-medium">Value</th>
                        <th className="text-left p-4 text-[#a0a0a0] font-medium">24h Change</th>
                        <th className="text-left p-4 text-[#a0a0a0] font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {portfolioItems.map((item, index) => (
                        <tr key={index} className="border-b border-[#2a2a2a] hover:bg-[#0a0a0a] transition-colors">
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] rounded-lg flex items-center justify-center text-sm">
                                {item.icon}
                              </div>
                              <div>
                                <div className="text-white font-medium">{item.token}</div>
                                <div className="text-[#a0a0a0] text-sm">{item.symbol}</div>
                              </div>
                            </div>
                          </td>
                          <td className="p-4 text-white">{item.holdings.toLocaleString()}</td>
                          <td className="p-4 text-white">${item.value.toLocaleString()}</td>
                          <td className="p-4">
                            <span className={`font-medium ${item.change24h >= 0 ? 'text-[#10b981]' : 'text-[#ef4444]'}`}>
                              {item.change24h >= 0 ? '+' : ''}{item.change24h}%
                            </span>
                          </td>
                          <td className="p-4">
                            <div className="flex gap-2">
                              <button className="bg-[#d8e9ea] text-black px-3 py-1 rounded text-xs font-medium hover:bg-[#b8d4d6] transition-colors">
                                Trade
                              </button>
                              <button className="bg-[#2a2a2a] text-white px-3 py-1 rounded text-xs font-medium hover:bg-[#3a3a3a] transition-colors">
                                Stake
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Activity Tab */}
          {activeTab === 'activity' && (
            <div className="space-y-6">
              <h3 className="text-white text-xl font-semibold">Activity Feed</h3>
              
              <div className="space-y-4">
                {activities.map((activity) => (
                  <div key={activity.id} className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-4">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] rounded-lg flex items-center justify-center text-lg">
                        {activity.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-white">{activity.message}</p>
                          {activity.amount && (
                            <span className="text-[#10b981] font-medium">${activity.amount}</span>
                          )}
                        </div>
                        <p className="text-[#a0a0a0] text-sm">{activity.timestamp}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="space-y-8">
              <h3 className="text-white text-xl font-semibold">Account Settings</h3>
              
              {/* Profile Settings */}
              <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-6">
                <h4 className="text-white font-medium mb-4">Profile Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-[#a0a0a0] text-sm mb-2 block">Username</label>
                    <input
                      type="text"
                      defaultValue={userProfile.username}
                      className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-3 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors"
                    />
                  </div>
                  <div>
                    <label className="text-[#a0a0a0] text-sm mb-2 block">Email</label>
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-3 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors"
                    />
                  </div>
                </div>
              </div>

              {/* Security Settings */}
              <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-6">
                <h4 className="text-white font-medium mb-4">Security</h4>
                <div className="space-y-4">
                  <div>
                    <label className="text-[#a0a0a0] text-sm mb-2 block">Current Password</label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-3 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors pr-12"
                        placeholder="Enter current password"
                      />
                      <button
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#a0a0a0] hover:text-white"
                      >
                        {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="text-[#a0a0a0] text-sm mb-2 block">New Password</label>
                    <input
                      type="password"
                      className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-3 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors"
                      placeholder="Enter new password"
                    />
                  </div>
                  <div className="flex items-center gap-3">
                    <button className="bg-[#d8e9ea] text-black px-4 py-2 rounded-lg font-medium hover:bg-[#b8d4d6] transition-colors">
                      <Shield size={16} className="inline mr-2" />
                      Enable 2FA
                    </button>
                  </div>
                </div>
              </div>

              {/* API Settings */}
              <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-6">
                <h4 className="text-white font-medium mb-4">API Keys</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-[#0a0a0a] border border-[#2a2a2a] rounded-lg">
                    <div>
                      <div className="text-white font-medium">Production API Key</div>
                      <div className="text-[#a0a0a0] text-sm">Last used: 2 hours ago</div>
                    </div>
                    <div className="flex gap-2">
                      <button className="bg-[#2a2a2a] text-white px-3 py-1 rounded text-sm hover:bg-[#3a3a3a] transition-colors">
                        <Copy size={14} className="inline mr-1" />
                        Copy
                      </button>
                      <button className="bg-[#ef4444] text-white px-3 py-1 rounded text-sm hover:bg-[#dc2626] transition-colors">
                        Revoke
                      </button>
                    </div>
                  </div>
                  <button className="bg-[#d8e9ea] text-black px-4 py-2 rounded-lg font-medium hover:bg-[#b8d4d6] transition-colors">
                    <Key size={16} className="inline mr-2" />
                    Generate New API Key
                  </button>
                </div>
              </div>

              {/* Notification Settings */}
              <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-xl p-6">
                <h4 className="text-white font-medium mb-4">Notifications</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Email Notifications</div>
                      <div className="text-[#a0a0a0] text-sm">Receive updates via email</div>
                    </div>
                    <button className="relative w-12 h-6 rounded-full bg-[#d8e9ea] transition-colors">
                      <div className="absolute top-1 right-1 w-4 h-4 bg-white rounded-full shadow-md"></div>
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Push Notifications</div>
                      <div className="text-[#a0a0a0] text-sm">Receive browser notifications</div>
                    </div>
                    <button className="relative w-12 h-6 rounded-full bg-[#2a2a2a] transition-colors">
                      <div className="absolute top-1 left-1 w-4 h-4 bg-white rounded-full shadow-md"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile; 