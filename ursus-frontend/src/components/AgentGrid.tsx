import React from 'react';
import Agent<PERSON><PERSON> from './AgentCard';
import { Agent } from '../types';

interface AgentGridProps {
  agents: Agent[];
  onCardClick: (agent: Agent) => void;
  onChatClick: (agent: Agent) => void;
  onTradeClick: (agent: Agent) => void;
}

const AgentGrid: React.FC<AgentGridProps> = ({ 
  agents, 
  onCardClick, 
  onChatClick, 
  onTradeClick 
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
      {agents.map((agent) => (
        <AgentCard
          key={agent.id}
          agent={agent}
          onCardClick={onCardClick}
          onChatClick={onChatClick}
          onTradeClick={onTradeClick}
        />
      ))}
    </div>
  );
};

export default AgentGrid;