import React, { useState, useEffect } from 'react';
import { Notification } from '../types';

const NotificationBar: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'buy',
      user: 'AIBot',
      amount: 0.0110,
      agent: 'CryptoLabs',
      marketCap: 32000,
      timestamp: new Date()
    },
    {
      id: '2',
      type: 'create',
      user: '9jEbqr',
      agent: '🔮PTGL🔮',
      timestamp: new Date()
    },
    {
      id: '3',
      type: 'buy',
      user: 'trader123',
      amount: 0.0055,
      agent: 'DeFi Master',
      marketCap: 15200,
      timestamp: new Date()
    }
  ]);

  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setScrollPosition(prev => prev - 1);
    }, 50);

    return () => clearInterval(interval);
  }, []);

  const formatNotification = (notification: Notification) => {
    switch (notification.type) {
      case 'buy':
        return (
          <span className="whitespace-nowrap">
            <span className="text-black px-2 py-1 rounded mr-2" style={{ backgroundColor: '#d8e9ea' }}>
              🤖 {notification.user} bought {notification.amount} SOL of {notification.agent} 🟡 | mcap: ${(notification.marketCap! / 1000).toFixed(1)}K
            </span>
          </span>
        );
      case 'create':
        return (
          <span className="whitespace-nowrap">
            <span className="text-black px-2 py-1 rounded mr-2" style={{ backgroundColor: '#d8e9ea' }}>
              👤 {notification.user} created {notification.agent}
            </span>
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="h-[50px] bg-[#0a0a0a] border-b border-[#2a2a2a] flex items-center overflow-hidden relative">
      <div 
        className="flex items-center gap-4 animate-scroll"
        style={{ transform: `translateX(${scrollPosition}px)` }}
      >
        {[...notifications, ...notifications, ...notifications].map((notification, index) => (
          <div key={`${notification.id}-${index}`} className="flex-shrink-0">
            {formatNotification(notification)}
          </div>
        ))}
      </div>
      
      <div className="absolute right-4 flex items-center gap-3">
        <button className="bg-[#d8e9ea] text-black px-4 py-2 rounded-lg font-medium hover:bg-[#b8d4d6] transition-colors">
          Create Agent
        </button>
        <button className="bg-[#2a2a2a] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#3a3a3a] transition-colors">
          Connect Wallet
        </button>
      </div>
    </div>
  );
};

export default NotificationBar;