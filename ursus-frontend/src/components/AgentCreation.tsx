import React, { useState } from 'react';
import { ChevronRight, Home, ArrowLeft, Check, Zap, Brain, Clock, Sparkles, Database, Languages, Upload, Coins, TrendingUp, DollarSign } from 'lucide-react';
import StepIndicator from './StepIndicator';
import TemplateCard from './TemplateCard';

interface AgentCreationProps {
  onBack: () => void;
}

const AgentCreation: React.FC<AgentCreationProps> = ({ onBack }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [instructions, setInstructions] = useState('');
  const [charCount, setCharCount] = useState(0);
  const [selectedModel, setSelectedModel] = useState('');
  const [responseTime, setResponseTime] = useState(50);
  const [creativity, setCreativity] = useState(50);
  const [memoryRetention, setMemoryRetention] = useState(true);
  const [multiLanguage, setMultiLanguage] = useState(false);
  const [tokenName, setTokenName] = useState('');
  const [tokenSymbol, setTokenSymbol] = useState('');
  const [initialSupply, setInitialSupply] = useState(100000000);
  const [tokenLogo, setTokenLogo] = useState<File | null>(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployProgress, setDeployProgress] = useState(0);
  const [isDeploySuccess, setIsDeploySuccess] = useState(false);

  const steps = [
    'Instructions',
    'Model & Settings', 
    'Token Configuration',
    'Deploy & Launch'
  ];

  const templates = [
    {
      title: 'DeFi Analyzer',
      icon: '💎',
      description: 'You are a DeFi research assistant that analyzes yield farming opportunities across Core Network protocols. Monitor liquidity pools, calculate APY rates, and provide risk assessments for various DeFi strategies.'
    },
    {
      title: 'Content Creator',
      icon: '📝',
      description: 'You are a creative content generator that produces engaging social media posts, blog articles, and marketing copy. Focus on viral content patterns, SEO optimization, and audience engagement strategies.'
    },
    {
      title: 'Trading Bot',
      icon: '⚡',
      description: 'You are a cryptocurrency trading analyst that provides real-time market analysis and trading signals. Use technical indicators, sentiment analysis, and on-chain data to generate actionable trading insights.'
    },
    {
      title: 'Research Assistant',
      icon: '🔬',
      description: 'You are a comprehensive research assistant that analyzes market trends, compiles reports, and provides data-driven insights. Synthesize information from multiple sources and present clear, actionable findings.'
    },
    {
      title: 'Social Media Manager',
      icon: '📱',
      description: 'You are a social media management expert that creates content calendars, analyzes engagement metrics, and optimizes posting strategies across multiple platforms to maximize reach and engagement.'
    },
    {
      title: 'Code Reviewer',
      icon: '⚙️',
      description: 'You are a smart contract auditor and code reviewer that identifies vulnerabilities, suggests optimizations, and ensures best practices in blockchain development. Focus on security and gas efficiency.'
    }
  ];

  const models = [
    {
      id: 'gpt4',
      name: 'OpenAI GPT-4',
      logo: '🤖',
      capabilities: 'Advanced reasoning, coding, analysis',
      pricing: '$0.03/1K tokens',
      performance: '95%',
      description: 'Most capable model for complex tasks'
    },
    {
      id: 'claude',
      name: 'Anthropic Claude',
      logo: '🧠',
      capabilities: 'Ethical AI, long conversations',
      pricing: '$0.025/1K tokens',
      performance: '92%',
      description: 'Great for thoughtful, nuanced responses'
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      logo: '✨',
      capabilities: 'Multimodal, fast processing',
      pricing: '$0.02/1K tokens',
      performance: '88%',
      description: 'Excellent for real-time applications'
    },
    {
      id: 'opensource',
      name: 'Open Source Options',
      logo: '🔓',
      capabilities: 'Customizable, cost-effective',
      pricing: '$0.01/1K tokens',
      performance: '78%',
      description: 'Budget-friendly with full control'
    }
  ];

  const handleInstructionsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 2000) {
      setInstructions(value);
      setCharCount(value.length);
    }
  };

  const handleTemplateSelect = (templateDescription: string) => {
    setInstructions(templateDescription);
    setCharCount(templateDescription.length);
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDeploy = async () => {
    setIsDeploying(true);
    setDeployProgress(0);
    
    // Simulate deployment progress
    const intervals = [
      { progress: 20, delay: 500, text: 'Initializing deployment...' },
      { progress: 40, delay: 1000, text: 'Creating smart contract...' },
      { progress: 60, delay: 1500, text: 'Configuring token parameters...' },
      { progress: 80, delay: 2000, text: 'Deploying to blockchain...' },
      { progress: 100, delay: 2500, text: 'Deployment complete!' }
    ];
    
    for (const interval of intervals) {
      await new Promise(resolve => setTimeout(resolve, interval.delay));
      setDeployProgress(interval.progress);
    }
    
    setIsDeploying(false);
    setIsDeploySuccess(true);
  };

  return (
    <div className="min-h-screen bg-[#0a0a0a] ml-[200px]">
      <div className="p-8">
        {/* Header Section */}
        <div className="mb-8">
          {/* Back Button */}
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-[#a0a0a0] hover:text-white mb-6 transition-colors group"
          >
            <ArrowLeft size={16} className="group-hover:-translate-x-1 transition-transform" />
            <span className="text-sm">Back to Home</span>
          </button>

          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-[#a0a0a0] mb-4">
            <Home size={14} />
            <span>Home</span>
            <ChevronRight size={14} />
            <span className="text-[#d8e9ea]">Agent Creation</span>
          </div>

          {/* Title */}
          <h1 className="text-white text-4xl font-bold mb-3 bg-gradient-to-r from-white to-[#d8e9ea] bg-clip-text text-transparent">
            Create Your AI Agent
          </h1>
          <p className="text-[#e5e5e5] text-lg">
            Deploy an AI agent with its own token in minutes
          </p>
        </div>

        {/* Step Indicator */}
        <StepIndicator currentStep={currentStep} steps={steps} />

        {/* Step 1: Instructions Panel */}
        {currentStep === 1 && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-10 shadow-2xl">
              {/* Section Title */}
              <div className="text-center mb-8">
                <h2 className="text-white text-3xl font-semibold mb-3">
                  Describe Your AI Agent
                </h2>
                <p className="text-[#a0a0a0] text-lg">
                  Tell your agent what to do and how to behave
                </p>
              </div>

              {/* Main Text Area */}
              <div className="mb-8">
                <div className="relative">
                  <textarea
                    value={instructions}
                    onChange={handleInstructionsChange}
                    placeholder="Tell your agent what to do. Example: 'You are a DeFi research assistant that analyzes yield farming opportunities across Core Network protocols...'"
                    className="w-full h-48 bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-6 text-white placeholder-[#666] resize-none focus:outline-none focus:border-[#d8e9ea] transition-colors text-base leading-relaxed"
                  />
                  
                  {/* Character Counter */}
                  <div className="absolute bottom-4 right-4 text-sm text-[#666]">
                    {charCount}/2000
                  </div>
                </div>
              </div>

              {/* Template Gallery */}
              <div className="mb-8">
                <h3 className="text-white text-xl font-medium mb-6 text-center">
                  Quick Start Templates
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {templates.map((template, index) => (
                    <TemplateCard
                      key={index}
                      title={template.title}
                      description={template.description}
                      icon={template.icon}
                      onSelect={handleTemplateSelect}
                    />
                  ))}
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-end pt-6 border-t border-[#2a2a2a]">
                <button
                  onClick={handleNext}
                  disabled={!instructions.trim()}
                  className="bg-[#d8e9ea] text-black px-8 py-3 rounded-xl font-semibold hover:bg-[#b8d4d6] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 shadow-lg hover:shadow-xl hover:shadow-[#d8e9ea]/25"
                >
                  Next: Model Selection
                  <ChevronRight size={18} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Model & Settings Panel */}
        {currentStep === 2 && (
          <div className="max-w-5xl mx-auto">
            <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-10 shadow-2xl">
              {/* Section Title */}
              <div className="text-center mb-10">
                <h2 className="text-white text-3xl font-semibold mb-3">
                  Choose AI Model & Configure Settings
                </h2>
                <p className="text-[#a0a0a0] text-lg">
                  Select the AI engine and customize your agent's behavior
                </p>
              </div>

              {/* AI Model Selection */}
              <div className="mb-12">
                <h3 className="text-white text-xl font-medium mb-6 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                    <Brain size={20} className="text-black" />
                  </div>
                  AI Model Selection
                </h3>
                
                <div className="grid grid-cols-2 gap-6">
                  {models.map((model) => (
                    <button
                      key={model.id}
                      onClick={() => setSelectedModel(model.id)}
                      className={`group relative p-6 bg-[#0a0a0a] border-2 rounded-xl text-left transition-all duration-300 hover:shadow-lg hover:shadow-[#d8e9ea]/10 ${
                        selectedModel === model.id
                          ? 'border-[#d8e9ea] bg-gradient-to-br from-[#0a0a0a] to-[#1a1a1a]'
                          : 'border-[#2a2a2a] hover:border-[#3a3a3a]'
                      }`}
                    >
                      {/* Selection Indicator */}
                      <div className={`absolute top-4 right-4 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all ${
                        selectedModel === model.id
                          ? 'border-[#d8e9ea] bg-[#d8e9ea]'
                          : 'border-[#666]'
                      }`}>
                        {selectedModel === model.id && (
                          <Check size={14} className="text-black animate-in fade-in duration-200" />
                        )}
                      </div>

                      {/* Model Info */}
                      <div className="flex items-start gap-4 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-[#d8e9ea] to-[#b8d4d6] rounded-xl flex items-center justify-center text-xl shadow-lg">
                          {model.logo}
                        </div>
                        <div className="flex-1">
                          <h4 className={`text-lg font-semibold mb-1 transition-colors ${
                            selectedModel === model.id ? 'text-[#d8e9ea]' : 'text-white'
                          }`}>
                            {model.name}
                          </h4>
                          <p className="text-[#a0a0a0] text-sm">
                            {model.description}
                          </p>
                        </div>
                      </div>

                      {/* Capabilities */}
                      <div className="mb-4">
                        <div className="text-[#e5e5e5] text-sm mb-2">
                          <strong>Capabilities:</strong> {model.capabilities}
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-[#10b981]">{model.pricing}</span>
                          <span className="text-[#d8e9ea]">Performance: {model.performance}</span>
                        </div>
                      </div>

                      {/* Hover Effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-[#d8e9ea]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />
                    </button>
                  ))}
                </div>
              </div>

              {/* Agent Settings */}
              <div className="mb-8">
                <h3 className="text-white text-xl font-medium mb-6 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                    <Zap size={20} className="text-black" />
                  </div>
                  Agent Settings
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Response Time Slider */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Clock size={18} className="text-[#d8e9ea]" />
                        <span className="text-white font-medium">Response Time</span>
                      </div>
                      <span className="text-[#a0a0a0] text-sm">
                        {responseTime < 30 ? 'Fast' : responseTime < 70 ? 'Balanced' : 'Thoughtful'}
                      </span>
                    </div>
                    <div className="relative">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={responseTime}
                        onChange={(e) => setResponseTime(Number(e.target.value))}
                        className="w-full h-2 bg-[#2a2a2a] rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #d8e9ea 0%, #d8e9ea ${responseTime}%, #2a2a2a ${responseTime}%, #2a2a2a 100%)`
                        }}
                      />
                    </div>
                  </div>

                  {/* Creativity Level Slider */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Sparkles size={18} className="text-[#d8e9ea]" />
                        <span className="text-white font-medium">Creativity Level</span>
                      </div>
                      <span className="text-[#a0a0a0] text-sm">
                        {creativity < 30 ? 'Precise' : creativity < 70 ? 'Balanced' : 'Creative'}
                      </span>
                    </div>
                    <div className="relative">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={creativity}
                        onChange={(e) => setCreativity(Number(e.target.value))}
                        className="w-full h-2 bg-[#2a2a2a] rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #d8e9ea 0%, #d8e9ea ${creativity}%, #2a2a2a ${creativity}%, #2a2a2a 100%)`
                        }}
                      />
                    </div>
                  </div>

                  {/* Memory Retention Toggle */}
                  <div className="flex items-center justify-between p-4 bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                        <Database size={16} className="text-black" />
                      </div>
                      <div>
                        <span className="text-white font-medium">Memory Retention</span>
                        <p className="text-[#a0a0a0] text-sm">Remember conversation context</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setMemoryRetention(!memoryRetention)}
                      className={`relative w-12 h-6 rounded-full transition-colors ${
                        memoryRetention ? 'bg-[#d8e9ea]' : 'bg-[#2a2a2a]'
                      }`}
                    >
                      <div className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow-md transition-transform ${
                        memoryRetention ? 'translate-x-7' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>

                  {/* Multi-language Support Toggle */}
                  <div className="flex items-center justify-between p-4 bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                        <Languages size={16} className="text-black" />
                      </div>
                      <div>
                        <span className="text-white font-medium">Multi-language Support</span>
                        <p className="text-[#a0a0a0] text-sm">Respond in multiple languages</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setMultiLanguage(!multiLanguage)}
                      className={`relative w-12 h-6 rounded-full transition-colors ${
                        multiLanguage ? 'bg-[#d8e9ea]' : 'bg-[#2a2a2a]'
                      }`}
                    >
                      <div className={`absolute top-1 w-4 h-4 bg-white rounded-full shadow-md transition-transform ${
                        multiLanguage ? 'translate-x-7' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6 border-t border-[#2a2a2a]">
                <button
                  onClick={handlePrevious}
                  className="bg-[#2a2a2a] text-white px-6 py-3 rounded-xl font-medium hover:bg-[#3a3a3a] transition-all duration-200 flex items-center gap-2"
                >
                  <ArrowLeft size={18} />
                  Previous
                </button>
                <button
                  onClick={handleNext}
                  disabled={!selectedModel}
                  className="bg-[#d8e9ea] text-black px-6 py-3 rounded-xl font-semibold hover:bg-[#b8d4d6] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 shadow-lg hover:shadow-xl hover:shadow-[#d8e9ea]/25"
                >
                  Next: Token Configuration
                  <ChevronRight size={18} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Token Configuration Panel */}
        {currentStep === 3 && (
          <div className="max-w-6xl mx-auto">
            <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-10 shadow-2xl">
              {/* Section Title */}
              <div className="text-center mb-10">
                <h2 className="text-white text-3xl font-semibold mb-3">
                  Configure Your Token
                </h2>
                <p className="text-[#a0a0a0] text-lg">
                  Set up your agent's token economics and branding
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column - Token Details */}
                <div className="space-y-6">
                  <h3 className="text-white text-xl font-medium mb-6 flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                      <Coins size={20} className="text-black" />
                    </div>
                    Token Details
                  </h3>

                  {/* Token Name Input */}
                  <div className="space-y-2">
                    <label className="text-white font-medium">Token Name</label>
                    <input
                      type="text"
                      value={tokenName}
                      onChange={(e) => setTokenName(e.target.value)}
                      placeholder="Enter token name (e.g., DeFi Analyzer Token)"
                      className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-4 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors"
                    />
                  </div>

                  {/* Token Symbol Input */}
                  <div className="space-y-2">
                    <label className="text-white font-medium">Token Symbol</label>
                    <div className="relative">
                      <input
                        type="text"
                        value={tokenSymbol}
                        onChange={(e) => setTokenSymbol(e.target.value.toUpperCase())}
                        placeholder="DAT"
                        maxLength={5}
                        className="w-full bg-[#0a0a0a] border-2 border-[#2a2a2a] rounded-xl p-4 text-white placeholder-[#666] focus:outline-none focus:border-[#d8e9ea] transition-colors uppercase"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#666] text-sm">
                        Auto-generated
                      </div>
                    </div>
                  </div>

                  {/* Initial Supply Slider */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <label className="text-white font-medium">Initial Supply</label>
                      <span className="text-[#a0a0a0] text-sm">
                        {initialSupply.toLocaleString()} tokens
                      </span>
                    </div>
                    <div className="relative">
                      <input
                        type="range"
                        min="1000000"
                        max="1000000000"
                        step="1000000"
                        value={initialSupply}
                        onChange={(e) => setInitialSupply(Number(e.target.value))}
                        className="w-full h-2 bg-[#2a2a2a] rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #d8e9ea 0%, #d8e9ea ${((initialSupply - 1000000) / (1000000000 - 1000000)) * 100}%, #2a2a2a ${((initialSupply - 1000000) / (1000000000 - 1000000)) * 100}%, #2a2a2a 100%)`
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-[#666]">
                      <span>1M</span>
                      <span>100M</span>
                      <span>1B</span>
                    </div>
                  </div>

                  {/* Token Logo Upload */}
                  <div className="space-y-2">
                    <label className="text-white font-medium">Token Logo</label>
                    <div className="border-2 border-dashed border-[#2a2a2a] rounded-xl p-8 text-center hover:border-[#d8e9ea] transition-colors cursor-pointer">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => setTokenLogo(e.target.files?.[0] || null)}
                        className="hidden"
                        id="token-logo-upload"
                      />
                      <label htmlFor="token-logo-upload" className="cursor-pointer">
                        <Upload size={32} className="text-[#666] mx-auto mb-3" />
                        <p className="text-[#a0a0a0] mb-2">
                          {tokenLogo ? tokenLogo.name : 'Click to upload logo'}
                        </p>
                        <p className="text-[#666] text-sm">PNG, JPG up to 2MB</p>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Right Column - Economics */}
                <div className="space-y-6">
                  <h3 className="text-white text-xl font-medium mb-6 flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                      <TrendingUp size={20} className="text-black" />
                    </div>
                    Token Economics
                  </h3>

                  {/* Bonding Curve Visualization */}
                  <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6">
                    <h4 className="text-white font-medium mb-4">Bonding Curve</h4>
                    <div className="h-32 bg-gradient-to-br from-[#1a1a1a] to-[#0a0a0a] rounded-lg border border-[#2a2a2a] relative overflow-hidden">
                      {/* Curve visualization */}
                      <div className="absolute inset-0 flex items-end">
                        <div className="w-full h-full bg-gradient-to-t from-[#d8e9ea]/20 to-transparent rounded-lg"></div>
                      </div>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6]"></div>
                    </div>
                    <p className="text-[#a0a0a0] text-sm mt-2">
                      Price increases with each token purchase
                    </p>
                  </div>

                  {/* Initial Price Calculation */}
                  <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6">
                    <h4 className="text-white font-medium mb-4">Initial Price</h4>
                    <div className="text-2xl font-bold text-[#d8e9ea] mb-2">
                      $0.001 CORE
                    </div>
                    <p className="text-[#a0a0a0] text-sm">
                      Starting price per token
                    </p>
                  </div>

                  {/* Market Cap Estimation */}
                  <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6">
                    <h4 className="text-white font-medium mb-4">Market Cap</h4>
                    <div className="text-2xl font-bold text-[#d8e9ea] mb-2">
                      ${(initialSupply * 0.001).toLocaleString()}
                    </div>
                    <p className="text-[#a0a0a0] text-sm">
                      Estimated initial market cap
                    </p>
                  </div>

                  {/* Fee Structure */}
                  <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6">
                    <h4 className="text-white font-medium mb-4">Fee Structure</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-[#a0a0a0]">Platform Fee</span>
                        <span className="text-[#d8e9ea]">2.5%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#a0a0a0]">Creator Royalty</span>
                        <span className="text-[#d8e9ea]">5%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-[#a0a0a0]">Liquidity Pool</span>
                        <span className="text-[#d8e9ea]">92.5%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6 border-t border-[#2a2a2a] mt-8">
                <button
                  onClick={handlePrevious}
                  className="bg-[#2a2a2a] text-white px-6 py-3 rounded-xl font-medium hover:bg-[#3a3a3a] transition-all duration-200 flex items-center gap-2"
                >
                  <ArrowLeft size={18} />
                  Previous
                </button>
                <button
                  onClick={handleNext}
                  disabled={!tokenName.trim() || !tokenSymbol.trim()}
                  className="bg-[#d8e9ea] text-black px-6 py-3 rounded-xl font-semibold hover:bg-[#b8d4d6] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 shadow-lg hover:shadow-xl hover:shadow-[#d8e9ea]/25"
                >
                  Next: Deploy & Launch
                  <ChevronRight size={18} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Step 4: Deploy & Launch Panel */}
        {currentStep === 4 && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-[#1a1a1a] border border-[#2a2a2a] rounded-2xl p-10 shadow-2xl">
              {/* Section Title */}
              <div className="text-center mb-10">
                <h2 className="text-white text-3xl font-semibold mb-3">
                  Deploy & Launch
                </h2>
                <p className="text-[#a0a0a0] text-lg">
                  Review costs and deploy your AI agent with token
                </p>
              </div>

              {/* Cost Breakdown */}
              <div className="mb-8">
                <h3 className="text-white text-xl font-medium mb-6 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-[#d8e9ea] to-[#b8d4d6] rounded-lg">
                    <DollarSign size={20} className="text-black" />
                  </div>
                  Cost Breakdown
                </h3>
                
                <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#a0a0a0]">Agent Deployment</span>
                    <span className="text-[#10b981] font-medium">Free</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#a0a0a0]">Platform Fee</span>
                    <span className="text-[#d8e9ea] font-medium">$2.50 CORE</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#a0a0a0]">Gas Estimation</span>
                    <span className="text-[#d8e9ea] font-medium">~$0.30</span>
                  </div>
                  <div className="border-t border-[#2a2a2a] pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-white font-semibold text-lg">Total Cost</span>
                      <span className="text-[#d8e9ea] font-bold text-lg">$2.80</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Agent Summary */}
              <div className="mb-8">
                <h3 className="text-white text-xl font-medium mb-6">Agent Summary</h3>
                <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-6 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-[#a0a0a0] text-sm">Agent Type</span>
                      <p className="text-white font-medium">{instructions.substring(0, 50)}...</p>
                    </div>
                    <div>
                      <span className="text-[#a0a0a0] text-sm">AI Model</span>
                      <p className="text-white font-medium">{models.find(m => m.id === selectedModel)?.name || 'Not selected'}</p>
                    </div>
                    <div>
                      <span className="text-[#a0a0a0] text-sm">Token Name</span>
                      <p className="text-white font-medium">{tokenName || 'Not set'}</p>
                    </div>
                    <div>
                      <span className="text-[#a0a0a0] text-sm">Token Symbol</span>
                      <p className="text-white font-medium">{tokenSymbol || 'Not set'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Deploy Button */}
              <div className="space-y-4">
                {!isDeploySuccess ? (
                  <button
                    onClick={handleDeploy}
                    disabled={isDeploying}
                    className="w-full h-[60px] bg-[#d8e9ea] text-black font-bold text-lg rounded-xl hover:bg-[#b8d4d6] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:shadow-[#d8e9ea]/25 flex items-center justify-center gap-3"
                  >
                    {isDeploying ? (
                      <>
                        <div className="w-6 h-6 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                        Deploying... {deployProgress}%
                      </>
                    ) : (
                      <>
                        <Zap size={20} />
                        Deploy Agent & Launch Token
                      </>
                    )}
                  </button>
                ) : (
                  <div className="w-full h-[60px] bg-[#10b981] text-white font-bold text-lg rounded-xl flex items-center justify-center gap-3">
                    <Check size={20} />
                    Deployment Successful!
                  </div>
                )}

                {/* Progress Bar */}
                {isDeploying && (
                  <div className="w-full bg-[#2a2a2a] rounded-full h-2">
                    <div 
                      className="bg-[#d8e9ea] h-2 rounded-full transition-all duration-500"
                      style={{ width: `${deployProgress}%` }}
                    ></div>
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6 border-t border-[#2a2a2a] mt-8">
                <button
                  onClick={handlePrevious}
                  className="bg-[#2a2a2a] text-white px-6 py-3 rounded-xl font-medium hover:bg-[#3a3a3a] transition-all duration-200 flex items-center gap-2"
                >
                  <ArrowLeft size={18} />
                  Previous
                </button>
                {isDeploySuccess && (
                  <button
                    onClick={onBack}
                    className="bg-[#d8e9ea] text-black px-6 py-3 rounded-xl font-semibold hover:bg-[#b8d4d6] transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl hover:shadow-[#d8e9ea]/25"
                  >
                    Back to Home
                    <ChevronRight size={18} />
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentCreation;
