export interface Agent {
  id: string;
  name: string;
  symbol: string;
  description: string;
  avatar: string;
  creator: string;
  createdAt: string;
  marketCap: number;
  chatCount: number;
  isNsfw: boolean;
  category: string;
  priceHistory: number[];
  priceChange24h: number;
}

export interface Notification {
  id: string;
  type: 'buy' | 'sell' | 'create';
  user: string;
  amount?: number;
  agent: string;
  marketCap?: number;
  timestamp: Date;
}

export interface User {
  id: string;
  username: string;
  balance: number;
  holdings: AgentHolding[];
}

export interface AgentHolding {
  agentId: string;
  agentName: string;
  amount: number;
  value: number;
}