import { useState, useEffect } from 'react';
import { Agent } from '../types';

export const useRealtimeUpdates = (initialAgents: Agent[]) => {
  const [agents, setAgents] = useState<Agent[]>(initialAgents);

  useEffect(() => {
    const interval = setInterval(() => {
      setAgents(prevAgents => 
        prevAgents.map(agent => ({
          ...agent,
          marketCap: agent.marketCap + Math.floor(Math.random() * 10000 - 5000),
          chatCount: agent.chatCount + Math.floor(Math.random() * 3),
          priceHistory: [
            ...agent.priceHistory.slice(1),
            agent.priceHistory[agent.priceHistory.length - 1] * (1 + (Math.random() - 0.5) * 0.05)
          ],
          priceChange24h: agent.priceChange24h + (Math.random() - 0.5) * 2
        }))
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return agents;
};