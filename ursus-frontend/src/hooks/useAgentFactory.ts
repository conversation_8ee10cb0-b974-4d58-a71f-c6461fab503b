import { useContractRead, useContractWrite, usePrepareContractWrite, useNetwork } from 'wagmi'
import { parseEther, formatEther } from 'viem'
import { useCallback } from 'react'
import { AGENT_FACTORY_ABI, getContractAddress } from '../config/contracts'

export interface AgentCreationParams {
  name: string
  symbol: string
  description: string
  instructions: string
  model: string
  category: string
}

export const useAgentFactory = () => {
  const { chain } = useNetwork()

  // Get contract address for current network
  const contractAddress = chain?.id ? getContractAddress('AGENT_FACTORY', chain.id) : null

  // Read creation fee
  const { data: creationFee, isLoading: isLoadingFee } = useContractRead({
    address: contractAddress as `0x${string}`,
    abi: AGENT_FACTORY_ABI,
    functionName: 'creationFee',
    enabled: !!contractAddress,
  })

  // Read total agents
  const { data: totalAgents, isLoading: isLoadingTotal } = useContractRead({
    address: contractAddress as `0x${string}`,
    abi: AGENT_FACTORY_ABI,
    functionName: 'getTotalAgents',
    enabled: !!contractAddress,
  })

  // Get all agents (paginated)
  const getAllAgents = useCallback(async (offset: number = 0, limit: number = 20) => {
    // This would be implemented with a direct contract call
    // For now, return empty array
    return { agents: [], total: 0 }
  }, [])

  // Get trending agents
  const getTrendingAgents = useCallback(async (limit: number = 10) => {
    // This would be implemented with a direct contract call
    // For now, return empty array
    return []
  }, [])

  // Get agents by creator
  const getAgentsByCreator = useCallback(async (creator: string) => {
    // This would be implemented with a direct contract call
    // For now, return empty array
    return []
  }, [])

  // Prepare create agent transaction
  const { config: createAgentConfig } = usePrepareContractWrite({
    address: contractAddress as `0x${string}`,
    abi: AGENT_FACTORY_ABI,
    functionName: 'createAgent',
    value: creationFee,
    enabled: !!contractAddress && !!creationFee,
  })

  const { write: createAgent, isLoading: isCreating, error: createError } = useContractWrite(createAgentConfig)

  // Create agent wrapper
  const createAgentToken = useCallback(async (params: AgentCreationParams) => {
    if (!createAgent) {
      throw new Error('Contract not ready')
    }

    try {
      const tx = await createAgent({
        args: [
          params.name,
          params.symbol,
          params.description,
          params.instructions,
          params.model,
          params.category,
        ],
      })
      return tx
    } catch (error) {
      console.error('Failed to create agent:', error)
      throw error
    }
  }, [createAgent])

  return {
    // Contract data
    creationFee: creationFee ? formatEther(creationFee) : '0',
    totalAgents: totalAgents ? Number(totalAgents) : 0,
    
    // Loading states
    isLoadingFee,
    isLoadingTotal,
    isCreating,
    
    // Actions
    createAgentToken,
    getAllAgents,
    getTrendingAgents,
    getAgentsByCreator,
    
    // Errors
    createError,
    
    // Contract info
    contractAddress,
  }
}
