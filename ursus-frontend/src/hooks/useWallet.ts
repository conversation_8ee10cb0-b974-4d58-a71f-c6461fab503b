import { useAccount, useConnect, useDisconnect, useBalance, useNetwork, useSwitchNetwork } from 'wagmi'
import { useCallback, useEffect } from 'react'
import { coreTestnet, coreMainnet } from '../config/chains'

export const useWallet = () => {
  const { address, isConnected, isConnecting, isReconnecting } = useAccount()
  const { connect, connectors, error: connectError, isLoading: isConnectLoading, pendingConnector } = useConnect()
  const { disconnect } = useDisconnect()
  const { chain } = useNetwork()
  const { switchNetwork, isLoading: isSwitchLoading } = useSwitchNetwork()
  
  // Get balance for connected account
  const { data: balance, isLoading: isBalanceLoading } = useBalance({
    address,
    enabled: !!address,
  })

  // Auto-switch to Core network if connected to wrong network
  useEffect(() => {
    if (isConnected && chain && ![coreTestnet.id, coreMainnet.id].includes(chain.id)) {
      switchNetwork?.(coreTestnet.id)
    }
  }, [isConnected, chain, switchNetwork])

  const connectWallet = useCallback(async (connectorId?: string) => {
    try {
      const connector = connectorId 
        ? connectors.find(c => c.id === connectorId) 
        : connectors[0] // Default to first connector (MetaMask)
      
      if (connector) {
        await connect({ connector })
      }
    } catch (error) {
      console.error('Failed to connect wallet:', error)
    }
  }, [connect, connectors])

  const disconnectWallet = useCallback(() => {
    disconnect()
  }, [disconnect])

  const switchToCore = useCallback(async (mainnet = false) => {
    const targetChainId = mainnet ? coreMainnet.id : coreTestnet.id
    if (switchNetwork && chain?.id !== targetChainId) {
      await switchNetwork(targetChainId)
    }
  }, [switchNetwork, chain])

  const isOnCoreNetwork = chain?.id === coreTestnet.id || chain?.id === coreMainnet.id
  const isOnTestnet = chain?.id === coreTestnet.id
  const isOnMainnet = chain?.id === coreMainnet.id

  return {
    // Connection state
    address,
    isConnected,
    isConnecting: isConnecting || isReconnecting || isConnectLoading,
    
    // Balance
    balance: balance?.formatted,
    balanceSymbol: balance?.symbol,
    isBalanceLoading,
    
    // Network state
    chain,
    isOnCoreNetwork,
    isOnTestnet,
    isOnMainnet,
    isSwitchLoading,
    
    // Actions
    connectWallet,
    disconnectWallet,
    switchToCore,
    
    // Available connectors
    connectors,
    pendingConnector,
    
    // Errors
    connectError,
  }
}
