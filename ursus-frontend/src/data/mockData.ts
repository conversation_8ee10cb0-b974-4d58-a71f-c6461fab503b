import { Agent } from '../types';

export const mockAgents: Agent[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> Maximizer',
    symbol: 'YIELD',
    description: 'Analyzes yield farming opportunities across Core Network protocols. Automated strategy recommendations with real-time risk assessment.',
    avatar: '💎',
    creator: 'alice',
    createdAt: '2024-01-15T10:30:00Z',
    marketCap: 452000,
    chatCount: 127,
    isNsfw: false,
    category: 'defi',
    priceHistory: [0.85, 0.88, 0.82, 0.89, 0.94, 0.91, 0.96, 0.93, 0.97, 1.02, 1.05, 1.08],
    priceChange24h: 12.3
  },
  {
    id: '2',
    name: 'Content Genius',
    symbol: 'WRITE',
    description: 'Creates engaging social media content, blog posts, and marketing copy. Trained on viral content patterns and engagement optimization.',
    avatar: '📝',
    creator: 'bob',
    createdAt: '2024-01-15T05:45:00Z',
    marketCap: 128000,
    chatCount: 89,
    isNsfw: false,
    category: 'content',
    priceHistory: [1.20, 1.15, 1.22, 1.18, 1.10, 1.08, 1.12, 1.07, 1.05, 1.02, 0.98, 0.95],
    priceChange24h: -8.7
  },
  {
    id: '3',
    name: 'Crypto Oracle',
    symbol: 'ORACLE',
    description: 'Real-time market analysis and trading signals. Technical analysis with sentiment monitoring across all major exchanges.',
    avatar: '⚡',
    creator: 'charlie',
    createdAt: '2024-01-15T13:15:00Z',
    marketCap: 789000,
    chatCount: 203,
    isNsfw: false,
    category: 'trading',
    priceHistory: [2.10, 2.25, 2.18, 2.32, 2.45, 2.51, 2.48, 2.55, 2.62, 2.58, 2.65, 2.71],
    priceChange24h: 15.2
  },
  {
    id: '4',
    name: 'NFT Hunter',
    symbol: 'HUNT',
    description: 'Discovers undervalued NFT collections and predicts trending projects. Advanced rarity analysis and floor price predictions.',
    avatar: '🔥',
    creator: 'diana',
    createdAt: '2024-01-15T08:20:00Z',
    marketCap: 334000,
    chatCount: 156,
    isNsfw: false,
    category: 'nft',
    priceHistory: [0.45, 0.42, 0.48, 0.44, 0.41, 0.39, 0.43, 0.46, 0.48, 0.52, 0.49, 0.47],
    priceChange24h: -2.1
  },
  {
    id: '5',
    name: 'Security Sentinel',
    symbol: 'GUARD',
    description: 'Smart contract auditing and vulnerability detection. Provides security assessments for DeFi protocols and token contracts.',
    avatar: '🛡️',
    creator: 'eve',
    createdAt: '2024-01-15T11:50:00Z',
    marketCap: 567000,
    chatCount: 94,
    isNsfw: false,
    category: 'security',
    priceHistory: [1.65, 1.72, 1.68, 1.75, 1.80, 1.85, 1.88, 1.92, 1.89, 1.95, 2.01, 2.08],
    priceChange24h: 18.9
  },
  {
    id: '6',
    name: 'Meme Machine',
    symbol: 'MEME',
    description: 'Generates viral memes and social media content. Understands current trends and creates engaging visual content automatically.',
    avatar: '🎭',
    creator: 'frank',
    createdAt: '2024-01-15T06:30:00Z',
    marketCap: 245000,
    chatCount: 278,
    isNsfw: false,
    category: 'entertainment',
    priceHistory: [0.30, 0.35, 0.32, 0.28, 0.31, 0.29, 0.33, 0.36, 0.34, 0.37, 0.35, 0.38],
    priceChange24h: 5.6
  },
  {
    id: '7',
    name: 'Code Reviewer',
    symbol: 'CODE',
    description: 'Automated code review and optimization suggestions. Supports multiple programming languages with security focus.',
    avatar: '⚙️',
    creator: 'grace',
    createdAt: '2024-01-15T09:15:00Z',
    marketCap: 412000,
    chatCount: 67,
    isNsfw: false,
    category: 'development',
    priceHistory: [1.15, 1.12, 1.18, 1.21, 1.19, 1.16, 1.13, 1.10, 1.14, 1.17, 1.20, 1.23],
    priceChange24h: 3.2
  },
  {
    id: '8',
    name: 'Trend Spotter',
    symbol: 'TREND',
    description: 'Identifies emerging trends across social media, technology, and finance. Early signal detection for investment opportunities.',
    avatar: '🎯',
    creator: 'henry',
    createdAt: '2024-01-15T12:40:00Z',
    marketCap: 198000,
    chatCount: 145,
    isNsfw: false,
    category: 'analysis',
    priceHistory: [0.75, 0.71, 0.68, 0.72, 0.69, 0.66, 0.63, 0.60, 0.58, 0.55, 0.52, 0.49],
    priceChange24h: -12.4
  },
  {
    id: '9',
    name: 'Liquidity Hunter',
    symbol: 'LIQ',
    description: 'Finds optimal liquidity pools and arbitrage opportunities. Cross-chain analysis for maximum yield generation.',
    avatar: '🌊',
    creator: 'iris',
    createdAt: '2024-01-15T07:25:00Z',
    marketCap: 623000,
    chatCount: 189,
    isNsfw: false,
    category: 'defi',
    priceHistory: [1.80, 1.85, 1.90, 1.88, 1.92, 1.95, 2.01, 2.05, 2.08, 2.12, 2.15, 2.19],
    priceChange24h: 9.8
  },
  {
    id: '10',
    name: 'News Aggregator',
    symbol: 'NEWS',
    description: 'Curates and summarizes crypto news from multiple sources. Real-time sentiment analysis and market impact predictions.',
    avatar: '📡',
    creator: 'jack',
    createdAt: '2024-01-15T04:10:00Z',
    marketCap: 356000,
    chatCount: 112,
    isNsfw: false,
    category: 'news',
    priceHistory: [0.92, 0.89, 0.91, 0.94, 0.90, 0.87, 0.85, 0.88, 0.86, 0.83, 0.80, 0.78],
    priceChange24h: -6.3
  },
  {
    id: '11',
    name: 'Portfolio Optimizer',
    symbol: 'PORT',
    description: 'Rebalances crypto portfolios based on risk tolerance and market conditions. Automated diversification strategies.',
    avatar: '💼',
    creator: 'kate',
    createdAt: '2024-01-15T14:05:00Z',
    marketCap: 445000,
    chatCount: 98,
    isNsfw: false,
    category: 'portfolio',
    priceHistory: [1.35, 1.38, 1.42, 1.45, 1.48, 1.52, 1.49, 1.53, 1.56, 1.59, 1.62, 1.65],
    priceChange24h: 11.1
  },
  {
    id: '12',
    name: 'Gaming Prophet',
    symbol: 'GAME',
    description: 'Predicts successful gaming tokens and NFT games. Analyzes player engagement and tokenomics for GameFi projects.',
    avatar: '🎲',
    creator: 'liam',
    createdAt: '2024-01-15T03:55:00Z',
    marketCap: 278000,
    chatCount: 234,
    isNsfw: false,
    category: 'gaming',
    priceHistory: [0.58, 0.55, 0.52, 0.49, 0.46, 0.44, 0.41, 0.38, 0.35, 0.33, 0.30, 0.28],
    priceChange24h: -19.8
  }
];

export const trendingAgents = mockAgents.slice(0, 6);