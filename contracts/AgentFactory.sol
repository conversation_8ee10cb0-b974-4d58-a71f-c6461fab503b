// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "./AgentToken.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title AgentFactory
 * @dev Factory contract for creating AI agent tokens on Core DAO
 */
contract AgentFactory is Ownable, ReentrancyGuard, Pausable {
    // Platform configuration
    uint256 public creationFee = 2.5 ether; // 2.5 CORE
    address public platformTreasury;
    
    // Agent registry
    mapping(address => bool) public isAgentToken;
    mapping(address => address[]) public creatorAgents;
    address[] public allAgents;
    
    // Agent metadata for indexing
    struct AgentMetadata {
        address tokenAddress;
        string name;
        string symbol;
        string description;
        string category;
        address creator;
        uint256 createdAt;
        bool isActive;
    }
    
    mapping(address => AgentMetadata) public agentMetadata;
    mapping(string => address[]) public agentsByCategory;
    
    // Events
    event AgentCreated(
        address indexed tokenAddress,
        address indexed creator,
        string name,
        string symbol,
        string description,
        string category
    );
    event AgentDeactivated(address indexed tokenAddress);
    event CreationFeeUpdated(uint256 newFee);
    event TreasuryUpdated(address newTreasury);
    
    constructor(address _platformTreasury) Ownable(msg.sender) {
        platformTreasury = _platformTreasury;
    }
    
    /**
     * @dev Create a new AI agent token
     * @param _name Token name
     * @param _symbol Token symbol
     * @param _description Agent description
     * @param _instructions Agent instructions/prompt
     * @param _model AI model used
     * @param _category Agent category
     */
    function createAgent(
        string memory _name,
        string memory _symbol,
        string memory _description,
        string memory _instructions,
        string memory _model,
        string memory _category
    ) external payable nonReentrant whenNotPaused {
        require(msg.value >= creationFee, "Insufficient creation fee");
        require(bytes(_name).length > 0, "Name cannot be empty");
        require(bytes(_symbol).length > 0, "Symbol cannot be empty");
        require(bytes(_description).length > 0, "Description cannot be empty");
        require(bytes(_instructions).length > 0, "Instructions cannot be empty");
        
        // Deploy new agent token
        AgentToken newAgent = new AgentToken(
            _name,
            _symbol,
            _description,
            _instructions,
            _model,
            msg.sender,
            platformTreasury
        );
        
        address agentAddress = address(newAgent);
        
        // Register agent
        isAgentToken[agentAddress] = true;
        creatorAgents[msg.sender].push(agentAddress);
        allAgents.push(agentAddress);
        agentsByCategory[_category].push(agentAddress);
        
        // Store metadata
        agentMetadata[agentAddress] = AgentMetadata({
            tokenAddress: agentAddress,
            name: _name,
            symbol: _symbol,
            description: _description,
            category: _category,
            creator: msg.sender,
            createdAt: block.timestamp,
            isActive: true
        });
        
        // Transfer creation fee to treasury
        if (msg.value > 0) {
            payable(platformTreasury).transfer(msg.value);
        }
        
        emit AgentCreated(agentAddress, msg.sender, _name, _symbol, _description, _category);
    }
    
    /**
     * @dev Get all agents created by a specific creator
     * @param _creator Creator address
     * @return Array of agent token addresses
     */
    function getAgentsByCreator(address _creator) external view returns (address[] memory) {
        return creatorAgents[_creator];
    }
    
    /**
     * @dev Get all agents in a specific category
     * @param _category Category name
     * @return Array of agent token addresses
     */
    function getAgentsByCategory(string memory _category) external view returns (address[] memory) {
        return agentsByCategory[_category];
    }
    
    /**
     * @dev Get all agents (paginated)
     * @param _offset Starting index
     * @param _limit Number of agents to return
     * @return agents Array of agent token addresses
     * @return total Total number of agents
     */
    function getAllAgents(uint256 _offset, uint256 _limit) 
        external 
        view 
        returns (address[] memory agents, uint256 total) 
    {
        total = allAgents.length;
        
        if (_offset >= total) {
            return (new address[](0), total);
        }
        
        uint256 end = _offset + _limit;
        if (end > total) {
            end = total;
        }
        
        agents = new address[](end - _offset);
        for (uint256 i = _offset; i < end; i++) {
            agents[i - _offset] = allAgents[i];
        }
    }
    
    /**
     * @dev Get trending agents based on recent activity
     * @param _limit Number of agents to return
     * @return Array of agent token addresses
     */
    function getTrendingAgents(uint256 _limit) external view returns (address[] memory) {
        uint256 totalAgents = allAgents.length;
        if (totalAgents == 0) {
            return new address[](0);
        }
        
        uint256 returnCount = _limit > totalAgents ? totalAgents : _limit;
        address[] memory trending = new address[](returnCount);
        
        // Simple implementation: return most recent agents
        // In production, this would use more sophisticated metrics
        uint256 startIndex = totalAgents > returnCount ? totalAgents - returnCount : 0;
        
        for (uint256 i = 0; i < returnCount; i++) {
            trending[i] = allAgents[startIndex + i];
        }
        
        return trending;
    }
    
    /**
     * @dev Deactivate an agent (only creator or owner)
     * @param _agentAddress Agent token address
     */
    function deactivateAgent(address _agentAddress) external {
        require(isAgentToken[_agentAddress], "Not a valid agent");
        AgentMetadata storage metadata = agentMetadata[_agentAddress];
        require(
            msg.sender == metadata.creator || msg.sender == owner(),
            "Not authorized"
        );
        require(metadata.isActive, "Agent already deactivated");
        
        metadata.isActive = false;
        emit AgentDeactivated(_agentAddress);
    }
    
    /**
     * @dev Update creation fee (only owner)
     * @param _newFee New creation fee in CORE
     */
    function updateCreationFee(uint256 _newFee) external onlyOwner {
        creationFee = _newFee;
        emit CreationFeeUpdated(_newFee);
    }
    
    /**
     * @dev Update platform treasury (only owner)
     * @param _newTreasury New treasury address
     */
    function updateTreasury(address _newTreasury) external onlyOwner {
        require(_newTreasury != address(0), "Invalid treasury address");
        platformTreasury = _newTreasury;
        emit TreasuryUpdated(_newTreasury);
    }
    
    /**
     * @dev Pause contract (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Get total number of agents
     */
    function getTotalAgents() external view returns (uint256) {
        return allAgents.length;
    }
    
    /**
     * @dev Check if address is a valid agent token
     * @param _address Address to check
     */
    function isValidAgent(address _address) external view returns (bool) {
        return isAgentToken[_address];
    }
}
