// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title AgentToken
 * @dev ERC20 token for AI agents with bonding curve mechanics
 */
contract AgentToken is ERC20, Ownable, ReentrancyGuard {
    // Agent metadata
    string public agentDescription;
    string public agentInstructions;
    string public agentModel;
    address public creator;
    uint256 public createdAt;
    
    // Bonding curve parameters
    uint256 public constant RESERVE_RATIO = 500000; // 50% in PPM
    uint256 public constant MAX_SUPPLY = ********** * 10**18; // 1B tokens
    uint256 public reserveBalance; // CORE balance in contract
    uint256 public currentSupply; // Current token supply
    
    // Platform fees
    uint256 public constant PLATFORM_FEE = 250; // 2.5% in basis points
    uint256 public constant CREATOR_FEE = 500; // 5% in basis points
    address public platformTreasury;
    
    // Events
    event TokensPurchased(address indexed buyer, uint256 coreAmount, uint256 tokensReceived);
    event TokensSold(address indexed seller, uint256 tokensAmount, uint256 coreReceived);
    event AgentInteraction(address indexed user, string message);
    
    constructor(
        string memory _name,
        string memory _symbol,
        string memory _description,
        string memory _instructions,
        string memory _model,
        address _creator,
        address _platformTreasury
    ) ERC20(_name, _symbol) Ownable(_creator) {
        agentDescription = _description;
        agentInstructions = _instructions;
        agentModel = _model;
        creator = _creator;
        createdAt = block.timestamp;
        platformTreasury = _platformTreasury;
        
        // Ownership already set in constructor
        
        // Initial mint to creator (1% of max supply)
        uint256 initialMint = MAX_SUPPLY / 100;
        _mint(_creator, initialMint);
        currentSupply = initialMint;
    }
    
    /**
     * @dev Calculate purchase return using bonding curve formula
     * @param _coreAmount Amount of CORE to spend
     * @return tokensReceived Amount of tokens to receive
     */
    function calculatePurchaseReturn(uint256 _coreAmount) public view returns (uint256) {
        if (_coreAmount == 0) return 0;
        
        // Bancor formula: tokensReceived = currentSupply * ((1 + coreAmount/reserveBalance)^(reserveRatio/1000000) - 1)
        // Simplified for gas efficiency
        uint256 baseAmount = (_coreAmount * currentSupply) / (reserveBalance + _coreAmount);
        return (baseAmount * RESERVE_RATIO) / 1000000;
    }
    
    /**
     * @dev Calculate sale return using bonding curve formula
     * @param _tokenAmount Amount of tokens to sell
     * @return coreReceived Amount of CORE to receive
     */
    function calculateSaleReturn(uint256 _tokenAmount) public view returns (uint256) {
        if (_tokenAmount == 0 || _tokenAmount > currentSupply) return 0;
        
        // Bancor formula: coreReceived = reserveBalance * (1 - (1 - tokenAmount/currentSupply)^(1000000/reserveRatio))
        // Simplified for gas efficiency
        uint256 baseAmount = (_tokenAmount * reserveBalance) / currentSupply;
        return (baseAmount * 1000000) / RESERVE_RATIO;
    }
    
    /**
     * @dev Purchase tokens with CORE
     */
    function purchaseTokens() external payable nonReentrant {
        require(msg.value > 0, "Must send CORE");
        require(currentSupply < MAX_SUPPLY, "Max supply reached");
        
        uint256 coreAmount = msg.value;
        uint256 tokensToMint = calculatePurchaseReturn(coreAmount);
        
        require(tokensToMint > 0, "Invalid purchase amount");
        require(currentSupply + tokensToMint <= MAX_SUPPLY, "Would exceed max supply");
        
        // Calculate fees
        uint256 platformFeeAmount = (coreAmount * PLATFORM_FEE) / 10000;
        uint256 creatorFeeAmount = (coreAmount * CREATOR_FEE) / 10000;
        uint256 reserveAmount = coreAmount - platformFeeAmount - creatorFeeAmount;
        
        // Update state
        reserveBalance += reserveAmount;
        currentSupply += tokensToMint;
        
        // Mint tokens to buyer
        _mint(msg.sender, tokensToMint);
        
        // Transfer fees
        if (platformFeeAmount > 0) {
            payable(platformTreasury).transfer(platformFeeAmount);
        }
        if (creatorFeeAmount > 0) {
            payable(creator).transfer(creatorFeeAmount);
        }
        
        emit TokensPurchased(msg.sender, coreAmount, tokensToMint);
    }
    
    /**
     * @dev Sell tokens for CORE
     * @param _tokenAmount Amount of tokens to sell
     */
    function sellTokens(uint256 _tokenAmount) external nonReentrant {
        require(_tokenAmount > 0, "Must sell positive amount");
        require(balanceOf(msg.sender) >= _tokenAmount, "Insufficient token balance");
        
        uint256 coreToReturn = calculateSaleReturn(_tokenAmount);
        require(coreToReturn > 0, "Invalid sale amount");
        require(address(this).balance >= coreToReturn, "Insufficient contract balance");
        
        // Calculate fees
        uint256 platformFeeAmount = (coreToReturn * PLATFORM_FEE) / 10000;
        uint256 creatorFeeAmount = (coreToReturn * CREATOR_FEE) / 10000;
        uint256 userAmount = coreToReturn - platformFeeAmount - creatorFeeAmount;
        
        // Update state
        reserveBalance -= coreToReturn;
        currentSupply -= _tokenAmount;
        
        // Burn tokens
        _burn(msg.sender, _tokenAmount);
        
        // Transfer CORE
        payable(msg.sender).transfer(userAmount);
        if (platformFeeAmount > 0) {
            payable(platformTreasury).transfer(platformFeeAmount);
        }
        if (creatorFeeAmount > 0) {
            payable(creator).transfer(creatorFeeAmount);
        }
        
        emit TokensSold(msg.sender, _tokenAmount, userAmount);
    }
    
    /**
     * @dev Record agent interaction (for analytics)
     * @param _message Interaction message
     */
    function recordInteraction(string calldata _message) external {
        emit AgentInteraction(msg.sender, _message);
    }
    
    /**
     * @dev Get current token price in CORE
     */
    function getCurrentPrice() public view returns (uint256) {
        if (currentSupply == 0) return 0;
        return (reserveBalance * 1e18) / currentSupply;
    }
    
    /**
     * @dev Get agent metadata
     */
    function getAgentInfo() external view returns (
        string memory description,
        string memory instructions,
        string memory model,
        address agentCreator,
        uint256 timestamp
    ) {
        return (agentDescription, agentInstructions, agentModel, creator, createdAt);
    }
    
    /**
     * @dev Get bonding curve info
     */
    function getBondingCurveInfo() external view returns (
        uint256 supply,
        uint256 reserve,
        uint256 price,
        uint256 marketCap
    ) {
        uint256 currentPrice = getCurrentPrice();
        return (currentSupply, reserveBalance, currentPrice, (currentSupply * currentPrice) / 1e18);
    }
}
